from flask import Flask, request
import stripe
from pyngrok import ngrok
import os

app = Flask(__name__)
stripe.api_key = "pk_test_51RYTXVLOe9N5YGhRgWz9OXdcYALDFL801SsvAd8oZny5AiQ4BRwPBVI2rg0aueMUoOGJH85FwMQf6jGUZVWRQxh500sHCPVncO"

endpoint_secret = "whsec_1t9VBwrw3AUO7EgobBCDk62iGOkkvdDa"


@app.route("/stripe-webhook", methods=["POST"])
def stripe_webhook():
    payload = request.data
    sig_header = request.headers.get("stripe-signature")

    try:
        event = stripe.Webhook.construct_event(payload, sig_header, endpoint_secret)
    except ValueError:
        return "Invalid payload", 400
    except stripe.error.SignatureVerificationError:
        return "Invalid signature", 400

    # Handle successful checkout
    if event["type"] == "checkout.session.completed":
        session = event["data"]["object"]
        customer_email = session.get("customer_email")
        uchat_user_id = session.get("client_reference_id")  # <-- Important!

        # Call UChat API to send confirmation message
        send_uchat_confirmation(uchat_user_id)

    return "", 200


def send_uchat_confirmation(uchat_user_id):
    import requests

    headers = {
        "Authorization": "Bearer POfCFVqmmzCH9nrLftD6Pe31Y8RqKlVvKp9QV2RcctKxnyhOI0YhIeFE1YIR",
        "Content-Type": "application/json",
    }
    payload = {
        "user_id": uchat_user_id,
        "content": {
            "type": "text",
            "text": "✅ Your payment was successful! Thank you for your purchase.",
        },
    }
    requests.post(
        "https://openapi.uchat.com.au/v1/message/send", headers=headers, json=payload
    )


if __name__ == "__main__":
    # Set up ngrok tunnel
    port = 5000

    # Optional: Set ngrok authtoken if you have one
    ngrok.set_auth_token("*************************************************")

    # Create ngrok tunnel
    public_url = ngrok.connect(port)
    print(f"\n🚀 Flask app is running on: http://localhost:{port}")
    print(f"🌐 Public URL (for Stripe webhook): {public_url}")
    print(f"📝 Your webhook endpoint URL: {public_url}/stripe-webhook")
    print(f"\n⚠️  Add this URL to your Stripe Dashboard: {public_url}/stripe-webhook")
    print("=" * 60)

    try:
        app.run(debug=True, port=port, use_reloader=False)
    except KeyboardInterrupt:
        print("\n🛑 Shutting down...")
        ngrok.disconnect(public_url)
        ngrok.kill()
